import json
import requests
from datetime import datetime, timedelta
import time
import csv

# Token variable - this can be updated by other functions
token = "eyJraWQiOiIrMTNcL2ZQZTRjMXpPNXBkQUJCeTlFbHc5eFFlVmdVYTIxaGpZYWdvNTBRZz0iLCJhbGciOiJSUzI1NiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ZQDsKrCqhaeEZnWNd2tV4NlWMHPsQLecBfuXt2cqZBpNLnWWnosYl75zbo0bOhIl_zcBw1ddkFeMo0Daol3UaG92iZs78JYWOQ5XXXQ4XaRofUmN0A0bQOXJEI7kfoeIS2OUN2Bs3Wa1fHrQdVYYxYI1bk1kdYleYW_FMde40ECE0fpZaOZ6LhJjfUscqmjkPHgALo69-uv_Zlf-DrDwKBKGbHUMMpWUHu3iyg4Ny7iH5vXNgx7K7tLAJj2jRev9-4wCUi93M8tejBEzTxqk_VVBELIYKBpjRbFHGfmX-6bSmh2nTIv-uC2y7cXrjp-gPlToyHpLir8lTejFVNtHLA"

# Read the fake names from file
with open('fakename.txt', 'r', encoding='utf-8') as f:
    fake_names = [line.strip() for line in f.readlines()]

# Read the sample codes from file
with open('samplecodes.json', 'r', encoding='utf-8') as f:
    sample_codes = json.load(f)['data']

# Simplified headers for both requests
headers = {
    'accept': 'application/json, text/plain, */*',
    'authorization': f'Bearer {token}',
    'content-type': 'application/json'
}

# Base request payload
base_payload = {
    "product_type": "B2C",
    "gender": "male",
    "year_only": False,
    "email": "",
    "validate_account": True,
    "phone_number": "**********",
    "address": "",
    "sample_type": "Máu",
    "account_id": "a8cecd14-1358-46c3-a269-370da9b96985",
    "sample_collector_name": "",
    "sample_receiver_name": "",
    "sample_recollection": False,
    "sale_pic_disabled": "GENESTORY",
    "sample_collection_date": "2025-08-14T10:46:00",
    "sample_collection_date_time": "2025-08-14T03:46:27.700Z",
    "sample_receipt_date": "2025-08-14T10:44:00",
    "sample_receipt_time": "2025-08-14T03:44:00.000Z",
    "product_name": "GeneHealth",
    "product_code": "61",
    "free_of_charge": True,
    "expected_report_release_date": "2025-09-02",
    "is_priority": False,
    "technology": "MICROARRAY",
    "note": "",
    "dob": "2025-08-01"
}

def send_registration_request(name, sample_code):
    url = 'http://0.0.0.0:8010/kits/registrations'
    
    # Create payload for this request
    payload = base_payload.copy()
    payload['full_name'] = name
    payload['name'] = name
    payload['samplecode'] = sample_code
    
    try:
        response = requests.post(url, headers=headers, json=payload)
        print(f"Registration request for {name} with sample code {sample_code}: Status {response.status_code}")
        return response.status_code
    except Exception as e:
        print(f"Error sending registration request for {name}: {str(e)}")
        return None

def send_scan_sample_request(sample_code):
    url = f'https://qa.genestory.ai/operation/kits/scan-sample/{sample_code}/1'
    barcode = None
    
    try:
        # First scan request
        response1 = requests.put(url, headers=headers, json=None)
        print(f"First scan request for {sample_code}: Status {response1.status_code}")
        
        # Second scan request
        response2 = requests.put(url, headers=headers, json=None)
        print(f"Second scan request for {sample_code}: Status {response2.status_code}")
        
        # Extract barcode from second response if successful
        if response2.status_code in [200, 201]:
            try:
                response_data = response2.json()
                if 'data' in response_data and 'barcode' in response_data['data']:
                    barcode = response_data['data']['barcode']
                    print(f"Extracted barcode for {sample_code}: {barcode}")
            except json.JSONDecodeError:
                print(f"Could not parse JSON response for {sample_code}")
        
        return response2.status_code, barcode
    except Exception as e:
        print(f"Error sending scan sample request for {sample_code}: {str(e)}")
        return None, None

def save_mapping_to_csv(mappings, filename='sample_barcode_mapping.csv'):
    try:
        with open(filename, 'w', newline='') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(['Sample Code', 'Barcode'])  # Header
            for sample_code, barcode in mappings.items():
                writer.writerow([sample_code, barcode])
        print(f"\nMapping saved to {filename}")
    except Exception as e:
        print(f"Error saving mapping to CSV: {str(e)}")

def main():
    successful_registrations = 0
    failed_registrations = 0
    successful_scans = 0
    failed_scans = 0
    sample_barcode_mapping = {}

    # Process only first 96 entries or less if there are fewer entries
    for i in range(min(96, len(fake_names), len(sample_codes))):
        if 'samplecode' not in sample_codes[i]:
            continue
            
        name = fake_names[i]
        sample_code = sample_codes[i]['samplecode']
        
        # Send registration request
        reg_status = send_registration_request(name, sample_code)
        
        if reg_status == 200 or reg_status == 201:
            successful_registrations += 1
            # If registration is successful, send scan requests
            scan_status, barcode = send_scan_sample_request(sample_code)
            if scan_status == 200 or scan_status == 201:
                successful_scans += 1
                if barcode:
                    sample_barcode_mapping[sample_code] = barcode
            else:
                failed_scans += 1
        else:
            failed_registrations += 1
            
        # Add a small delay between requests to prevent overwhelming the server
        time.sleep(0.5)

    # Save the mapping to CSV file
    save_mapping_to_csv(sample_barcode_mapping)

    print(f"\nSummary:")
    print(f"Successful registrations: {successful_registrations}")
    print(f"Failed registrations: {failed_registrations}")
    print(f"Successful scans: {successful_scans}")
    print(f"Failed scans: {failed_scans}")
    print(f"Total registration requests sent: {successful_registrations + failed_registrations}")
    print(f"Total scan requests sent: {successful_scans + failed_scans}")
    print(f"Total barcodes collected: {len(sample_barcode_mapping)}")

if __name__ == "__main__":
    main()