import json
import requests
import time
import csv
import random

# Token variable
token = "eyJraWQiOiIrMTNcL2ZQZTRjMXpPNXBkQUJCeTlFbHc5eFFlVmdVYTIxaGpZYWdvNTBRZz0iLCJhbGciOiJSUzI1NiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.EmPNMi7q--EsY4vOytacAls9JF0sLOfO44DjPOqCvLms_bUJIuX-CaL0nzhPOb7zjiSdkFwR17PCHNnA3dJ2IU5yukZt5bWJ7wdH64jzQqB9mOUVBX_1x7O6srkNhDAxe5eLQepXVhErx72zi7CfPH2lJaRhYAmTE1r8HJ1PBe67I60-YFssFqq9owOV-JU-M0m-fDIIAN10oU6M6oThg2RF0DMAjyeO5Zwa9fIZYjk4PkFT-4jQq3JXl9_yjqk0RASnyE4OrZue4T5A1EGk8RTfMxHccIhoQPXyA1aQ-Ad393hWFaa02Fk-E7mqX5MKWBR1hUv26tGCAraXweXBWg"

# Simple headers
headers = {
    'accept': 'application/json, text/plain, */*',
    'authorization': f'Bearer {token}',
    'content-type': 'application/json'
}

def read_lids_from_csv(filename='sample_barcode_mapping_with_lid.csv'):
    """Read LIDs from the CSV file."""
    lids = []
    try:
        with open(filename, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            for row in reader:
                lid = row.get('LID', '').strip()
                if lid:
                    lids.append(lid)
        print(f"Read {len(lids)} LIDs from {filename}")
        return lids
    except FileNotFoundError:
        print(f"File {filename} not found")
        return []
    except Exception as e:
        print(f"Error reading {filename}: {str(e)}")
        return []

def generate_dna_data(lid):
    """Generate random DNA extraction data for a given LID."""
    return {
        "lid": lid,
        "dna_extraction_date": "2025-03-03",
        "qubit": round(random.uniform(50.0, 80.0), 1),
        "nano_drop": round(random.uniform(130.0, 180.0), 3),
        "a260_a280": round(random.uniform(1.8, 1.9), 3),
        "agarose_gel": "PASS",
        "dna_qc_status": "PASS"
    }

def send_dna_extraction_check(lids):
    """Send DNA extraction check request."""
    url = 'https://qa.genestory.ai/operation/lims/dna_extractions/check'
    
    # Generate data for all LIDs
    payload = [generate_dna_data(lid) for lid in lids]
    
    try:
        response = requests.post(url, headers=headers, json=payload)
        print(f"DNA extraction check request: Status {response.status_code}")
        if response.status_code not in [200, 201]:
            print(f"Response: {response.text}")
        return response.status_code
    except Exception as e:
        print(f"Error sending DNA extraction check request: {str(e)}")
        return None

def send_dna_extraction_create(lids):
    """Send DNA extraction create request."""
    url = 'https://qa.genestory.ai/operation/lims/dna_extractions'
    
    # Generate data for all LIDs
    payload = [generate_dna_data(lid) for lid in lids]
    
    try:
        response = requests.post(url, headers=headers, json=payload)
        print(f"DNA extraction create request: Status {response.status_code}")
        if response.status_code not in [200, 201]:
            print(f"Response: {response.text}")
        return response.status_code
    except Exception as e:
        print(f"Error sending DNA extraction create request: {str(e)}")
        return None

def main():
    # Read LIDs from CSV file
    lids = read_lids_from_csv()
    
    if not lids:
        print("No LIDs found. Exiting.")
        return
    
    print(f"Found {len(lids)} LIDs to process")
    print(f"First few LIDs: {lids[:5]}")
    print(f"Last few LIDs: {lids[-5:]}")
    
    # Send first request - DNA extraction check
    print("\n=== Sending DNA extraction check request ===")
    check_status = send_dna_extraction_check(lids)
    
    # Add delay between requests
    time.sleep(2)
    
    # Send second request - DNA extraction create
    print("\n=== Sending DNA extraction create request ===")
    create_status = send_dna_extraction_create(lids)
    
    print(f"\nSummary:")
    print(f"Total LIDs processed: {len(lids)}")
    print(f"DNA extraction check status: {check_status}")
    print(f"DNA extraction create status: {create_status}")
    
    if check_status in [200, 201] and create_status in [200, 201]:
        print("✅ Both requests completed successfully!")
    else:
        print("❌ One or both requests failed. Check the logs above for details.")

if __name__ == "__main__":
    main()
