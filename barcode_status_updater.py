#!/usr/bin/env python3
"""
Script to update lab sample status for barcodes from CSV file.
Reads barcodes from sample_barcode_mapping.csv and sends status update requests.
"""

import csv
import json
import requests
import time
from typing import List, Dict, Optional
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class BarcodeStatusUpdater:
    def __init__(self, csv_file: str = "sample_barcode_mapping.csv"):
        self.csv_file = csv_file
        self.api_url = "https://qa.genestory.ai/operation/lims/lab_sample/status"
        self.headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'en-US,en;q=0.9,vi-VN;q=0.8,vi;q=0.7',
            'authorization': 'Bearer eyJraWQiOiIrMTNcL2ZQZTRjMXpPNXBkQUJCeTlFbHc5eFFlVmdVYTIxaGpZYWdvNTBRZz0iLCJhbGciOiJSUzI1NiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.CJ4OYVOkBCuqOjcrjZNDaln90cwH4d29Ih7-ZeeDlu62ZlejG7-w-KasMCNDHJZcItW626UXTPeLfSSOLDOYVoWFru_Y5dX2POuM6XwxL0OyCQ3xFAJ8VpQgWZvZeJULvhl1IQoCclKmlb4svjmM_dmXze6KMnEFH4a8RGKxdX-YmYYSSrIRqACsJqctp7qoWaeZu-qX4Crti1R_8TJnZjp_U1asFx51VlHuKpa5QVgyGYFKDHrs2LxtXKMoEkoNfVEuiHWHYGtdc64Cbn-W365WsGwCA1Zk7qpNGKXfu5mLtz-Y5sj1DkCHhoRBpu7deCX98UfiDbSVFsjiVSpjXA',
            'content-type': 'application/json',
            'origin': 'https://qa.genestory.ai',
            'priority': 'u=1, i',
            'referer': 'https://qa.genestory.ai/lims/sample-info/gate-1',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Linux"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
            'Cookie': 'wp-wpml_current_language=vi; _ga=GA1.2.1901061896.1748246285; _ga_VX1389DHCK=GS2.1.s1755071567$o57$g1$t1755073387$j53$l0$h0'
        }
        
    def read_barcodes(self) -> List[Dict[str, str]]:
        """Read barcodes from CSV file."""
        barcodes = []
        try:
            with open(self.csv_file, 'r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                for row in reader:
                    if row.get('Barcode') and row.get('Barcode').strip():
                        barcodes.append({
                            'sample_code': row.get('SampleCode', '').strip(),
                            'barcode': row.get('Barcode', '').strip()
                        })
            logger.info(f"Successfully read {len(barcodes)} barcodes from {self.csv_file}")
            return barcodes
        except FileNotFoundError:
            logger.error(f"CSV file {self.csv_file} not found")
            return []
        except Exception as e:
            logger.error(f"Error reading CSV file: {e}")
            return []
    
    def send_status_update(self, barcode: str, status: str = "PASSED_LAB_CHECK", note: str = "") -> Optional[Dict]:
        """Send status update request for a single barcode."""
        payload = {
            "barcode": barcode,
            "status": status,
            "note": note
        }
        
        try:
            response = requests.post(
                self.api_url,
                headers=self.headers,
                data=json.dumps(payload),
                timeout=30
            )
            
            if response.status_code == 200:
                logger.info(f"✅ Successfully updated barcode {barcode}")
                return response.json() if response.content else {"success": True}
            else:
                logger.error(f"❌ Failed to update barcode {barcode}. Status: {response.status_code}, Response: {response.text}")
                return None
                
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ Request failed for barcode {barcode}: {e}")
            return None
    
    def update_all_barcodes(self, status: str = "PASSED_LAB_CHECK", note: str = "", delay: float = 0.5) -> Dict[str, int]:
        """Update status for all barcodes from CSV file."""
        barcodes = self.read_barcodes()
        
        if not barcodes:
            logger.error("No barcodes found to process")
            return {"total": 0, "success": 0, "failed": 0}
        
        results = {"total": len(barcodes), "success": 0, "failed": 0}
        
        logger.info(f"Starting to update {len(barcodes)} barcodes...")
        
        for i, barcode_info in enumerate(barcodes, 1):
            barcode = barcode_info['barcode']
            sample_code = barcode_info['sample_code']
            
            logger.info(f"Processing {i}/{len(barcodes)}: {sample_code} -> {barcode}")
            
            result = self.send_status_update(barcode, status, note)
            
            if result:
                results["success"] += 1
            else:
                results["failed"] += 1
            
            # Add delay between requests to avoid overwhelming the server
            if delay > 0 and i < len(barcodes):
                time.sleep(delay)
        
        logger.info(f"Completed! Total: {results['total']}, Success: {results['success']}, Failed: {results['failed']}")
        return results

def main():
    """Main function to run the barcode status updater."""
    updater = BarcodeStatusUpdater()
    
    # You can customize these parameters:
    status = "PASSED_LAB_CHECK"  # Change this if needed
    note = ""  # Add a note if needed
    delay = 0.5  # Delay between requests in seconds
    
    print("🚀 Starting barcode status update process...")
    print(f"📁 Reading from: {updater.csv_file}")
    print(f"🎯 API endpoint: {updater.api_url}")
    print(f"📊 Status to set: {status}")
    print(f"⏱️  Delay between requests: {delay}s")
    print("-" * 50)
    
    results = updater.update_all_barcodes(status=status, note=note, delay=delay)
    
    print("-" * 50)
    print("📈 Final Results:")
    print(f"   Total barcodes: {results['total']}")
    print(f"   Successful: {results['success']}")
    print(f"   Failed: {results['failed']}")
    
    if results['failed'] > 0:
        print("⚠️  Some requests failed. Check the logs above for details.")
    else:
        print("🎉 All requests completed successfully!")

if __name__ == "__main__":
    main()
