import json
import requests
import time
import csv

# Token variable
token = "eyJraWQiOiIrMTNcL2ZQZTRjMXpPNXBkQUJCeTlFbHc5eFFlVmdVYTIxaGpZYWdvNTBRZz0iLCJhbGciOiJSUzI1NiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.CJ4OYVOkBCuqOjcrjZNDaln90cwH4d29Ih7-ZeeDlu62ZlejG7-w-KasMCNDHJZcItW626UXTPeLfSSOLDOYVoWFru_Y5dX2POuM6XwxL0OyCQ3xFAJ8VpQgWZvZeJULvhl1IQoCclKmlb4svjmM_dmXze6KMnEFH4a8RGKxdX-YmYYSSrIRqACsJqctp7qoWaeZu-qX4Crti1R_8TJnZjp_U1asFx51VlHuKpa5QVgyGYFKDHrs2LxtXKMoEkoNfVEuiHWHYGtdc64Cbn-W365WsGwCA1Zk7qpNGKXfu5mLtz-Y5sj1DkCHhoRBpu7deCX98UfiDbSVFsjiVSpjXA"

# Simple headers
headers = {
    'accept': 'application/json, text/plain, */*',
    'authorization': f'Bearer {token}',
    'content-type': 'application/json'
}

def read_sample_barcode_mapping(filename='sample_barcode_mapping.csv'):
    """Read the existing sample barcode mapping from CSV file."""
    mapping = {}
    try:
        with open(filename, 'r', encoding='utf-8') as file:
            reader = csv.DictReader(file)
            for row in reader:
                sample_code = row.get('SampleCode', '').strip()
                barcode = row.get('Barcode', '').strip()
                if sample_code and barcode:
                    mapping[sample_code] = {'barcode': barcode, 'lid': None}
        print(f"Read {len(mapping)} entries from {filename}")
        return mapping
    except FileNotFoundError:
        print(f"File {filename} not found")
        return {}
    except Exception as e:
        print(f"Error reading {filename}: {str(e)}")
        return {}

def get_lid_from_barcode(barcode):
    """Get lid from barcode using the API."""
    url = f'https://qa.genestory.ai/operation/lims/lab_sample/samples/lab_check/passed?barcode={barcode}'
    
    try:
        response = requests.get(url, headers=headers)
        print(f"GET request for barcode {barcode}: Status {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if 'data' in data and len(data['data']) > 0:
                lid = data['data'][0].get('lid')
                samplecode = data['data'][0].get('samplecode')
                print(f"Found lid {lid} for barcode {barcode} (samplecode: {samplecode})")
                return lid, samplecode
            else:
                print(f"No data found for barcode {barcode}")
                return None, None
        else:
            print(f"Failed to get data for barcode {barcode}: {response.text}")
            return None, None
            
    except Exception as e:
        print(f"Error getting lid for barcode {barcode}: {str(e)}")
        return None, None

def save_updated_mapping_to_csv(mapping, filename='sample_barcode_mapping_with_lid.csv'):
    """Save the updated mapping with lid information to CSV file."""
    try:
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(['SampleCode', 'Barcode', 'LID'])  # Header
            for sample_code, info in mapping.items():
                writer.writerow([sample_code, info['barcode'], info['lid'] or ''])
        print(f"\nUpdated mapping saved to {filename}")
    except Exception as e:
        print(f"Error saving updated mapping to CSV: {str(e)}")

def main():
    # Read existing mapping
    mapping = read_sample_barcode_mapping()
    
    if not mapping:
        print("No mapping data found. Exiting.")
        return
    
    successful_lid_requests = 0
    failed_lid_requests = 0
    
    print(f"Starting to get LID for {len(mapping)} barcodes...")
    
    for i, (sample_code, info) in enumerate(mapping.items(), 1):
        barcode = info['barcode']
        print(f"\nProcessing {i}/{len(mapping)}: {sample_code} -> {barcode}")
        
        lid, returned_samplecode = get_lid_from_barcode(barcode)
        
        if lid:
            mapping[sample_code]['lid'] = lid
            successful_lid_requests += 1
            
            # Verify samplecode matches
            if returned_samplecode and returned_samplecode != sample_code:
                print(f"WARNING: Sample code mismatch! Expected: {sample_code}, Got: {returned_samplecode}")
        else:
            failed_lid_requests += 1
        
        # Add a small delay between requests to prevent overwhelming the server
        time.sleep(0.5)
    
    # Save updated mapping
    save_updated_mapping_to_csv(mapping)
    
    print(f"\nSummary:")
    print(f"Successful LID requests: {successful_lid_requests}")
    print(f"Failed LID requests: {failed_lid_requests}")
    print(f"Total requests sent: {successful_lid_requests + failed_lid_requests}")
    
    # Show some statistics
    lids_found = sum(1 for info in mapping.values() if info['lid'])
    print(f"Total LIDs found: {lids_found}")
    print(f"Total entries in mapping: {len(mapping)}")

if __name__ == "__main__":
    main()
