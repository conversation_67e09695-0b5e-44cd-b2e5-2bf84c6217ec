import json
import requests
import time

# Token variable
token = "eyJraWQiOiIrMTNcL2ZQZTRjMXpPNXBkQUJCeTlFbHc5eFFlVmdVYTIxaGpZYWdvNTBRZz0iLCJhbGciOiJSUzI1NiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.CJ4OYVOkBCuqOjcrjZNDaln90cwH4d29Ih7-ZeeDlu62ZlejG7-w-KasMCNDHJZcItW626UXTPeLfSSOLDOYVoWFru_Y5dX2POuM6XwxL0OyCQ3xFAJ8VpQgWZvZeJULvhl1IQoCclKmlb4svjmM_dmXze6KMnEFH4a8RGKxdX-YmYYSSrIRqACsJqctp7qoWaeZu-qX4Crti1R_8TJnZjp_U1asFx51VlHuKpa5QVgyGYFKDHrs2LxtXKMoEkoNfVEuiHWHYGtdc64Cbn-W365WsGwCA1Zk7qpNGKXfu5mLtz-Y5sj1DkCHhoRBpu7deCX98UfiDbSVFsjiVSpjXA"

# List of 96 barcodes
barcodes = [
    "055540116361", "033648389261", "381835529361", "706731851261", "051061427861",
    "089298205661", "669942561861", "202634398161", "237512801961", "708400404361",
    "317383674261", "616547463361", "911421063961", "201210455361", "046957805661",
    "549447988461", "462439173961", "943080444961", "092605622861", "199646756161",
    "954430413661", "062736528961", "304212679561", "062266832061", "725585699161",
    "125234154161", "789942215761", "874700937761", "813873452661", "723206885361",
    "273075598861", "657816490661", "265569575361", "572070882961", "790209724361",
    "083787923661", "387658999761", "673583888861", "103232420861", "195702412961",
    "041108449361", "193995662061", "239230796761", "032408102561", "109389042961",
    "652332216161", "766440264461", "692844844661", "392206537361", "289849217161",
    "234133003561", "710884315061", "630072617661", "362892219261", "812884774761",
    "161515469761", "347970809461", "490935799361", "778927813861", "770077662761",
    "645704889761", "062323362761", "866147133661", "615598527061", "330844926261",
    "475341986161", "630703474661", "067592511161", "765712900461", "611833556161",
    "760092338261", "030781902361", "130162867161", "804307244361", "827169335361",
    "228553319361", "322859872861", "577880625561", "812290669761", "251763872261",
    "160585334461", "558753525961", "720884495861", "885896326161", "819516335961",
    "528077989561", "348485192161", "678529125561", "100774108861", "726377546861",
    "008882618661", "716183277161", "157736056661", "349350599561", "960924889061",
    "644904427261"
]

# Simple headers like in 2_register_info.py
headers = {
    'accept': 'application/json, text/plain, */*',
    'authorization': f'Bearer {token}',
    'content-type': 'application/json'
}

def send_status_update(barcode, status="PASSED_LAB_CHECK", note=""):
    url = 'https://qa.genestory.ai/operation/lims/lab_sample/status'
    
    payload = {
        "barcode": barcode,
        "status": status,
        "note": note
    }
    
    try:
        response = requests.post(url, headers=headers, json=payload)
        print(f"Status update for barcode {barcode}: Status {response.status_code}")
        return response.status_code
    except Exception as e:
        print(f"Error sending status update for barcode {barcode}: {str(e)}")
        return None

def main():
    successful_updates = 0
    failed_updates = 0
    
    print(f"Starting to update status for {len(barcodes)} barcodes...")
    
    for i, barcode in enumerate(barcodes, 1):
        print(f"Processing {i}/{len(barcodes)}: {barcode}")
        
        status_code = send_status_update(barcode)
        
        if status_code == 200 or status_code == 201:
            successful_updates += 1
        else:
            failed_updates += 1
        
        # Add a small delay between requests to prevent overwhelming the server
        time.sleep(0.5)
    
    print(f"\nSummary:")
    print(f"Successful updates: {successful_updates}")
    print(f"Failed updates: {failed_updates}")
    print(f"Total requests sent: {successful_updates + failed_updates}")

if __name__ == "__main__":
    main()
